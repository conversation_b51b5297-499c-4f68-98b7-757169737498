import { useEffect, useRef, useCallback } from 'react';
import type { DownloadItem, AppState } from '../../types/download';
import { ExtensionStatus } from '../../types/download';
import {
  getRequestIdFromUrl,
  generatePageTaskId,
  requestDownloadData,
  createMessageListener
} from '../../lib/download/messageUtils';

interface UseDownloaderInitProps {
  state: AppState;
  setPageTaskId: (pageTaskId: string) => void;
  setContentScriptReady: (ready: boolean) => void;
  setExtensionStatus: (status: ExtensionStatus) => void;
  setDownloadData: (data: DownloadItem | null) => void;
  setError: (error: string) => void;
  startDownloadWithData: (data: DownloadItem) => Promise<void>;
}

/**
 * 下载器初始化Hook
 * 负责页面初始化、消息监听和数据加载
 */
export function useDownloaderInit({
  state,
  setPageTaskId,
  setContentScriptReady,
  setExtensionStatus,
  setDownloadData,
  setError,
  startDownloadWithData
}: UseDownloaderInitProps) {
  const cleanupFunctions = useRef<Array<() => void>>([]);
  const startDownloadWithDataRef = useRef(startDownloadWithData);

  // 更新ref以获取最新的函数
  useEffect(() => {
    startDownloadWithDataRef.current = startDownloadWithData;
  }, [startDownloadWithData]);

  // 初始化页面任务ID
  useEffect(() => {
    const pageTaskId = generatePageTaskId();
    setPageTaskId(pageTaskId);
  }, [setPageTaskId]);

  // 设置消息监听器和主动探测插件
  useEffect(() => {
    // 添加全局消息监听器用于调试
    const globalListener = (event: MessageEvent) => {
      // 移除详细日志，只在开发环境下输出
      if (process.env.NODE_ENV === 'development' && event.source === window) {
        console.log('收到消息:', event.data.type);
      }
    };
    window.addEventListener('message', globalListener);

    const cleanup1 = createMessageListener(async (event) => {
      const { type, ...messageData } = event.data;

      if (type === 'EXTENSION_PONG') {
        // 插件对ping的回应，说明插件存在且正常工作
        console.log('插件已安装并正常工作');
        setContentScriptReady(true);
      } else if (type === 'CONTENT_SCRIPT_READY') {
        // 兼容旧的就绪消息
        console.log('内容脚本已准备就绪');
        setContentScriptReady(true);
      } else if (type === 'HEADERS_SET_COMPLETED') {
        // 检查消息是否属于当前页面
        if (messageData && messageData.pageTaskId && messageData.pageTaskId === state.pageTaskId) {
          if (messageData.success) {
            console.log('请求头设置成功');
          } else {
            console.error('请求头设置失败:', messageData.error);
          }
        }
      } else if (type === 'DOWNLOAD_DATA_RESPONSE' || type === 'SET_REQUEST_HEADERS_RESPONSE' ||
        type === 'DOWNLOAD_FILE_RESPONSE' || type === 'CLEANUP_REQUEST_HEADERS_RESPONSE') {
        // 这些是通过新的Chrome标准消息传递方式发送的响应
        // 检查消息是否属于当前页面
        const actualPageTaskId = messageData.data?.pageTaskId || messageData.pageTaskId;

        if (messageData && actualPageTaskId && actualPageTaskId === state.pageTaskId) {
          // 只在开发环境下输出详细信息
          if (process.env.NODE_ENV === 'development') {
            console.log(`收到${type}响应`);
          }
          // 触发相应的处理逻辑
          window.dispatchEvent(new CustomEvent(type, { detail: messageData }));
        }
      }
    });

    cleanupFunctions.current.push(cleanup1);

    // 主动探测插件函数
    const pingExtension = () => {
      window.postMessage({
        type: 'EXTENSION_PING',
        timestamp: Date.now(),
        pageTaskId: state.pageTaskId
      }, window.location.origin);
    };

    // 立即开始探测
    pingExtension();

    // 重试机制：每500ms探测一次，最多探测6次（3秒内）
    let retryCount = 0;
    const maxRetries = 6;
    const retryInterval = setInterval(() => {
      if (!state.contentScriptReady && retryCount < maxRetries) {
        retryCount++;
        // 只在开发环境下输出重试信息
        if (process.env.NODE_ENV === 'development') {
          console.log(`重试探测插件 (${retryCount}/${maxRetries})`);
        }
        pingExtension();
      } else {
        clearInterval(retryInterval);
      }
    }, 500);

    // 最终超时机制 - 3秒后如果还没有响应，认为插件未安装
    const extensionDetectionTimer = setTimeout(() => {
      if (!state.contentScriptReady && state.extensionStatus === ExtensionStatus.DETECTING) {
        console.log('插件检测超时，未检测到SnapAny插件');
        setExtensionStatus(ExtensionStatus.NOT_INSTALLED);
      }
      clearInterval(retryInterval);
    }, 3000);

    return () => {
      clearTimeout(extensionDetectionTimer);
      clearInterval(retryInterval);
      window.removeEventListener('message', globalListener);
      cleanupFunctions.current.forEach(cleanup => cleanup());
      cleanupFunctions.current = [];
    };
  }, [state.pageTaskId, setContentScriptReady, setExtensionStatus, state.contentScriptReady, state.extensionStatus]);

  // 从URL加载下载数据
  const loadDownloadDataFromUrl = useCallback(() => {
    try {
      const requestId = getRequestIdFromUrl();

      if (requestId && state.pageTaskId) {
        // 只在开发环境下输出详细信息
        if (process.env.NODE_ENV === 'development') {
          console.log('从URL获取请求ID:', requestId);
        }

        const cleanup = requestDownloadData(
          requestId,
          state.pageTaskId,
          (downloadData: DownloadItem) => {
            console.log('收到下载数据，开始下载');
            setDownloadData(downloadData);

            // 自动开始下载
            setTimeout(async () => {
              await startDownloadWithDataRef.current(downloadData);
            }, 100); // 稍微延迟确保状态更新完成
          },
          (error: string) => {
            setError(error);
          }
        );

        cleanupFunctions.current.push(cleanup);
      } else {
        console.warn('URL中没有找到请求ID');
        setError('没有找到请求ID');
      }
    } catch (error) {
      console.error('解析URL参数失败:', error);
      setError('解析URL参数失败: ' + (error as Error).message);
    }
  }, [state.pageTaskId, setDownloadData, setError]);

  // 当内容脚本和页面都准备就绪时，开始加载下载数据
  useEffect(() => {
    if (state.contentScriptReady && state.pageReady && state.pageTaskId) {
      // 只在开发环境下输出详细信息
      if (process.env.NODE_ENV === 'development') {
        console.log('内容脚本和页面都已准备就绪，开始加载下载数据');
      }
      loadDownloadDataFromUrl();
    }
  }, [state.contentScriptReady, state.pageReady, state.pageTaskId, loadDownloadDataFromUrl]);

  return {
    cleanupFunctions
  };
}
